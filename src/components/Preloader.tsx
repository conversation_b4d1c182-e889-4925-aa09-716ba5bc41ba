'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
// import Image from 'next/image'; // Uncomment when logo.png is available

interface PreloaderProps {
  onComplete: () => void;
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [greetingText, setGreetingText] = useState('');
  const [secondText, setSecondText] = useState('');

  const greeting = "Hi darling, welcome to Urvashi...";
  const secondLine = "Let's begin something special.";

  // Typing effect for greeting
  useEffect(() => {
    if (currentStep === 0) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= greeting.length) {
          setGreetingText(greeting.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(1), 500);
        }
      }, 50);
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Typing effect for second line
  useEffect(() => {
    if (currentStep === 1) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= secondLine.length) {
          setSecondText(secondLine.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(2), 1000);
        }
      }, 70);
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Logo animation phase
  useEffect(() => {
    if (currentStep === 2) {
      setTimeout(() => setCurrentStep(3), 2500);
    }
  }, [currentStep]);

  // Final transition
  useEffect(() => {
    if (currentStep === 3) {
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 800);
      }, 1000);
    }
  }, [currentStep, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black overflow-hidden">
      {/* Subtle ambient glow */}
      <div className="absolute inset-0 bg-gradient-radial from-[#F66581]/5 via-transparent to-transparent"></div>

      {/* Main content container */}
      <div className="relative z-10 text-center max-w-lg mx-auto px-6">

        {/* Greeting Phase */}
        {currentStep >= 0 && (
          <div className={`transition-opacity duration-1000 ${currentStep >= 2 ? 'opacity-0' : 'opacity-100'}`}>
            <p className="text-2xl md:text-3xl text-white font-light mb-4 min-h-[2.5rem]">
              {greetingText}
              {currentStep === 0 && <span className="animate-pulse">|</span>}
            </p>

            {currentStep >= 1 && (
              <p className="text-lg md:text-xl text-gray-300 font-light min-h-[1.5rem]">
                {secondText}
                {currentStep === 1 && <span className="animate-pulse">|</span>}
              </p>
            )}
          </div>
        )}

        {/* Logo Animation Phase */}
        {currentStep >= 2 && (
          <div className={`transition-all duration-1000 ${currentStep === 2 ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
            }`}>

            {/* Logo container with elegant reveal */}
            <div className="relative mb-8">
              {/* Soft glow background */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-32 h-32 bg-[#F66581]/20 rounded-full blur-2xl animate-pulse"></div>
              </div>

              {/* Logo image */}
              <div className="relative flex items-center justify-center">
                <div className="w-32 h-14 md:w-56 md:h-20 relative">
                  {/* Fallback heart icon if logo image doesn't exist */}
                  {/* <div className="w-full h-full bg-gradient-to-br from-[#F66581] to-[#F66581]/80 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
                    <svg
                      className="w-12 h-12 md:w-14 md:h-14 text-white"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                  </div> */}

                  {/* Uncomment when logo.png is available */}
                  <Image
                    src="/logo.png"
                    alt="Urvashi Logo"
                    fill
                    className="object-contain animate-pulse"
                    priority
                  />
                </div>
              </div>
            </div>

            {/* Brand name with elegant fade-in */}
            <div className="space-y-2">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-2 animate-pulse">
                <span className="bg-gradient-to-r from-[#F66581] to-[#F66581]/80 bg-clip-text text-transparent">
                  Urvashi
                </span>
              </h1>

              <p className="text-lg text-gray-300 font-light tracking-wide opacity-80">
                Your Indian AI Girlfriend
              </p>
            </div>
          </div>
        )}

        {/* Final transition curtain effect */}
        {currentStep === 3 && (
          <div className="absolute inset-0 bg-black animate-pulse"></div>
        )}
      </div>

      {/* Elegant transition overlay */}
      <div className={`absolute inset-0 bg-gradient-to-t from-black via-transparent to-black transition-opacity duration-1000 ${currentStep === 3 ? 'opacity-100' : 'opacity-0'
        }`}></div>
    </div>
  );
}
